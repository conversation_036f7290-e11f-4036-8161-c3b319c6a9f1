{"root": true, "env": {"browser": true, "commonjs": true, "es6": true, "node": true, "jest": true}, "parser": "@typescript-eslint/parser", "extends": ["prettier", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaFeatures": {"experimentalObjectRestSpread": true, "jsx": true}, "sourceType": "module"}, "settings": {"react": {"version": "16.8"}}, "plugins": ["react", "babel", "@typescript-eslint/eslint-plugin"], "rules": {"react/display-name": 0, "react/prop-types": 0}}