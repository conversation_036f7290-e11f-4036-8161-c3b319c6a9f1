import React from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
} from '@arco-design/web-react';
import { IconSearch, IconPlus } from '@arco-design/web-react/icon';

const { RangePicker } = DatePicker;

export default function InvoiceList() {
  const columns = [
    {
      title: '发票号码',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
    },
    {
      title: '开票日期',
      dataIndex: 'invoiceDate',
      key: 'invoiceDate',
    },
    {
      title: '购买方',
      dataIndex: 'buyer',
      key: 'buyer',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (value) => `¥${value}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          normal: { text: '正常', color: 'green' },
          cancelled: { text: '已作废', color: 'red' },
          pending: { text: '待处理', color: 'orange' },
        };
        const config = statusMap[status] || { text: status, color: 'gray' };
        return <span style={{ color: config.color }}>{config.text}</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button type="text" size="small">
            查看
          </Button>
          <Button type="text" size="small">
            下载
          </Button>
          <Button type="text" size="small" status="danger">
            作废
          </Button>
        </Space>
      ),
    },
  ];

  const data = [
    {
      key: '1',
      invoiceNumber: '12345678901234567890',
      invoiceDate: '2024-01-15',
      buyer: '浙江大学医学院附属第二医院',
      amount: '1,280.00',
      status: 'normal',
    },
    {
      key: '2',
      invoiceNumber: '12345678901234567891',
      invoiceDate: '2024-01-14',
      buyer: '杭州市第一人民医院',
      amount: '2,560.00',
      status: 'pending',
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Input
              placeholder="请输入发票号码"
              prefix={<IconSearch />}
              style={{ width: 200 }}
            />
            <Select
              placeholder="请选择状态"
              style={{ width: 120 }}
              options={[
                { label: '全部', value: 'all' },
                { label: '正常', value: 'normal' },
                { label: '已作废', value: 'cancelled' },
                { label: '待处理', value: 'pending' },
              ]}
            />
            <RangePicker placeholder={['开始日期', '结束日期']} />
            <Button type="primary" icon={<IconSearch />}>
              查询
            </Button>
            <Button icon={<IconPlus />}>新增发票</Button>
          </Space>
        </div>
        <Table
          columns={columns}
          data={data}
          pagination={{
            total: 2,
            pageSize: 10,
            current: 1,
          }}
        />
      </Card>
    </div>
  );
}
