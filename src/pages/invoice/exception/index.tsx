import React from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
} from '@arco-design/web-react';
import { IconSearch, IconExclamationCircle } from '@arco-design/web-react/icon';

export default function InvoiceException() {
  const columns = [
    {
      title: '发票号码',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
    },
    {
      title: '异常类型',
      dataIndex: 'exceptionType',
      key: 'exceptionType',
      render: (type) => {
        const typeMap = {
          format_error: { text: '格式错误', color: 'red' },
          amount_mismatch: { text: '金额不符', color: 'orange' },
          duplicate: { text: '重复开票', color: 'purple' },
          tax_error: { text: '税率错误', color: 'blue' },
        };
        const config = typeMap[type] || { text: type, color: 'gray' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '发现时间',
      dataIndex: 'foundTime',
      key: 'foundTime',
    },
    {
      title: '购买方',
      dataIndex: 'buyer',
      key: 'buyer',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (value) => `¥${value}`,
    },
    {
      title: '处理状态',
      dataIndex: 'handleStatus',
      key: 'handleStatus',
      render: (status) => {
        const statusMap = {
          pending: { text: '待处理', color: 'orange' },
          processing: { text: '处理中', color: 'blue' },
          resolved: { text: '已解决', color: 'green' },
          ignored: { text: '已忽略', color: 'gray' },
        };
        const config = statusMap[status] || { text: status, color: 'gray' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button type="text" size="small">
            查看详情
          </Button>
          {record.handleStatus === 'pending' && (
            <>
              <Button type="text" size="small" status="success">
                处理
              </Button>
              <Button type="text" size="small" status="warning">
                忽略
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  const data = [
    {
      key: '1',
      invoiceNumber: '12345678901234567892',
      exceptionType: 'format_error',
      foundTime: '2024-01-15 14:30:00',
      buyer: '浙江大学医学院附属第二医院',
      amount: '1,280.00',
      handleStatus: 'pending',
    },
    {
      key: '2',
      invoiceNumber: '12345678901234567893',
      exceptionType: 'amount_mismatch',
      foundTime: '2024-01-14 16:45:00',
      buyer: '杭州市第一人民医院',
      amount: '2,560.00',
      handleStatus: 'processing',
    },
    {
      key: '3',
      invoiceNumber: '12345678901234567894',
      exceptionType: 'duplicate',
      foundTime: '2024-01-13 09:15:00',
      buyer: '浙江省人民医院',
      amount: '3,200.00',
      handleStatus: 'resolved',
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Input
              placeholder="请输入发票号码"
              prefix={<IconSearch />}
              style={{ width: 200 }}
            />
            <Select
              placeholder="异常类型"
              style={{ width: 120 }}
              options={[
                { label: '全部', value: 'all' },
                { label: '格式错误', value: 'format_error' },
                { label: '金额不符', value: 'amount_mismatch' },
                { label: '重复开票', value: 'duplicate' },
                { label: '税率错误', value: 'tax_error' },
              ]}
            />
            <Select
              placeholder="处理状态"
              style={{ width: 120 }}
              options={[
                { label: '全部', value: 'all' },
                { label: '待处理', value: 'pending' },
                { label: '处理中', value: 'processing' },
                { label: '已解决', value: 'resolved' },
                { label: '已忽略', value: 'ignored' },
              ]}
            />
            <Button type="primary" icon={<IconSearch />}>
              查询
            </Button>
          </Space>
        </div>
        <Table
          columns={columns}
          data={data}
          pagination={{
            total: 3,
            pageSize: 10,
            current: 1,
          }}
        />
      </Card>
    </div>
  );
}
