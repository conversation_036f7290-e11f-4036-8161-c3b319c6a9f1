import React, { useEffect, useState } from 'react';
import { Spin } from '@arco-design/web-react';
import Footer from '@/components/Footer';
import Logo from '@/assets/logo.svg';
import LoginForm from './form';
import LoginBanner from './banner';
import { isAuthenticated } from '@/services/auth';
import styles from './style/index.module.less';

function Login() {
  const [checking, setChecking] = useState(true);

  useEffect(() => {
    document.body.setAttribute('arco-theme', 'light');

    // 检查用户是否已经登录，如果已登录则跳转到首页
    if (isAuthenticated()) {
      window.location.href = '/';
      return;
    }

    // 检查完成，显示登录页面
    setChecking(false);
  }, []);

  // 如果正在检查登录状态，显示加载中
  if (checking) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: '#f7f8fa',
        }}
      >
        <Spin size={40} />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.logo}>
        <Logo />
        <div className={styles['logo-text']}>浙二眼科电子发票平台</div>
      </div>
      <div className={styles.banner}>
        <div className={styles['banner-inner']}>
          <LoginBanner />
        </div>
      </div>
      <div className={styles.content}>
        <div className={styles['content-inner']}>
          <LoginForm />
        </div>
        <div className={styles.footer}>
          <Footer />
        </div>
      </div>
    </div>
  );
}
Login.displayName = 'LoginPage';

export default Login;
