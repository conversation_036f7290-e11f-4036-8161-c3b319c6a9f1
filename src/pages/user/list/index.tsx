import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Avatar,
  Tag,
  Modal,
  Form,
  Message,
} from '@arco-design/web-react';
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconDelete,
  IconUser,
} from '@arco-design/web-react/icon';

const FormItem = Form.Item;

export default function UserList() {
  const [visible, setVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [form] = Form.useForm();

  const columns = [
    {
      title: '头像',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 80,
      render: (avatar, record) => (
        <Avatar size={40}>
          {avatar ? <img src={avatar} alt="avatar" /> : record.name?.charAt(0)}
        </Avatar>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => {
        const roleMap = {
          admin: { text: '管理员', color: 'red' },
          doctor: { text: '医生', color: 'blue' },
          nurse: { text: '护士', color: 'green' },
          finance: { text: '财务', color: 'orange' },
        };
        const config = roleMap[role] || { text: role, color: 'gray' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          active: { text: '正常', color: 'green' },
          inactive: { text: '禁用', color: 'red' },
          pending: { text: '待激活', color: 'orange' },
        };
        const config = statusMap[status] || { text: status, color: 'gray' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={<IconEdit />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            size="small"
            status={record.status === 'active' ? 'warning' : 'success'}
            onClick={() => handleToggleStatus(record)}
          >
            {record.status === 'active' ? '禁用' : '启用'}
          </Button>
          <Button
            type="text"
            size="small"
            status="danger"
            icon={<IconDelete />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const data = [
    {
      key: '1',
      username: 'admin',
      name: '系统管理员',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      createTime: '2024-01-01 10:00:00',
      avatar: null,
    },
    {
      key: '2',
      username: 'doctor001',
      name: '张医生',
      email: '<EMAIL>',
      role: 'doctor',
      status: 'active',
      createTime: '2024-01-02 14:30:00',
      avatar: null,
    },
    {
      key: '3',
      username: 'nurse001',
      name: '李护士',
      email: '<EMAIL>',
      role: 'nurse',
      status: 'active',
      createTime: '2024-01-03 09:15:00',
      avatar: null,
    },
    {
      key: '4',
      username: 'finance001',
      name: '王会计',
      email: '<EMAIL>',
      role: 'finance',
      status: 'inactive',
      createTime: '2024-01-04 16:45:00',
      avatar: null,
    },
  ];

  const handleAdd = () => {
    setEditingUser(null);
    form.resetFields();
    setVisible(true);
  };

  const handleEdit = (record) => {
    setEditingUser(record);
    form.setFieldsValue(record);
    setVisible(true);
  };

  const handleDelete = (record) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除用户 "${record.name}" 吗？`,
      onOk: () => {
        Message.success('删除成功');
      },
    });
  };

  const handleToggleStatus = (record) => {
    const action = record.status === 'active' ? '禁用' : '启用';
    Modal.confirm({
      title: `确认${action}`,
      content: `确定要${action}用户 "${record.name}" 吗？`,
      onOk: () => {
        Message.success(`${action}成功`);
      },
    });
  };

  const handleSubmit = () => {
    form
      .validate()
      .then((values) => {
        console.log('Form values:', values);
        Message.success(editingUser ? '更新成功' : '创建成功');
        setVisible(false);
      })
      .catch((error) => {
        console.log('Validation failed:', error);
      });
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space wrap>
            <Input
              placeholder="请输入用户名或姓名"
              prefix={<IconSearch />}
              style={{ width: 200 }}
            />
            <Select
              placeholder="请选择角色"
              style={{ width: 120 }}
              options={[
                { label: '全部', value: 'all' },
                { label: '管理员', value: 'admin' },
                { label: '医生', value: 'doctor' },
                { label: '护士', value: 'nurse' },
                { label: '财务', value: 'finance' },
              ]}
            />
            <Select
              placeholder="请选择状态"
              style={{ width: 120 }}
              options={[
                { label: '全部', value: 'all' },
                { label: '正常', value: 'active' },
                { label: '禁用', value: 'inactive' },
                { label: '待激活', value: 'pending' },
              ]}
            />
            <Button type="primary" icon={<IconSearch />}>
              查询
            </Button>
            <Button icon={<IconPlus />} onClick={handleAdd}>
              新增用户
            </Button>
          </Space>
        </div>
        <Table
          columns={columns}
          data={data}
          pagination={{
            total: 4,
            pageSize: 10,
            current: 1,
          }}
        />
      </Card>

      <Modal
        title={editingUser ? '编辑用户' : '新增用户'}
        visible={visible}
        onOk={handleSubmit}
        onCancel={() => setVisible(false)}
        autoFocus={false}
        focusLock={true}
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="用户名"
            field="username"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </FormItem>
          <FormItem
            label="姓名"
            field="name"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="请输入姓名" />
          </FormItem>
          <FormItem
            label="邮箱"
            field="email"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入正确的邮箱格式' },
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </FormItem>
          <FormItem
            label="角色"
            field="role"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select
              placeholder="请选择角色"
              options={[
                { label: '管理员', value: 'admin' },
                { label: '医生', value: 'doctor' },
                { label: '护士', value: 'nurse' },
                { label: '财务', value: 'finance' },
              ]}
            />
          </FormItem>
          <FormItem
            label="状态"
            field="status"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select
              placeholder="请选择状态"
              options={[
                { label: '正常', value: 'active' },
                { label: '禁用', value: 'inactive' },
                { label: '待激活', value: 'pending' },
              ]}
            />
          </FormItem>
        </Form>
      </Modal>
    </div>
  );
}
