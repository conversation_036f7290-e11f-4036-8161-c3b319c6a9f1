# Request 网络请求库使用说明

这是一个基于 axios 封装的网络请求库，提供了无感刷新 JWT token 的功能。

## 📁 文件结构

```
src/
├── utils/
│   ├── request.js              # 主要的请求库文件
│   ├── request.example.js      # 使用示例
│   └── request.README.md       # 使用说明（本文件）
├── services/
│   └── auth.js                 # 认证相关API服务
├── components/
│   └── AuthGuard/
│       └── index.tsx           # 路由守卫组件
└── pages/
    └── login/
        └── form.new.tsx        # 更新后的登录表单示例
```

## 功能特性

- ✅ 基于 axios 封装，保持原有 API 的使用习惯
- ✅ 自动添加 Authorization 请求头
- ✅ 无感刷新 JWT token，用户无需重新登录
- ✅ 请求队列管理，避免并发请求时的 token 冲突
- ✅ 自动错误处理和重定向
- ✅ 支持常用的 HTTP 方法（GET、POST、PUT、DELETE、PATCH）
- ✅ 支持文件上传和下载
- ✅ 提供路由守卫组件
- ✅ TypeScript 友好（虽然本文件是 JS，但可以轻松迁移）

## 基本使用

```javascript
import api from '@/utils/request';

// GET 请求
const response = await api.get('/users', { page: 1, size: 10 });

// POST 请求
const response = await api.post('/users', {
  name: 'John',
  email: '<EMAIL>',
});

// PUT 请求
const response = await api.put('/users/1', { name: 'Jane' });

// DELETE 请求
const response = await api.delete('/users/1');
```

## Token 管理

```javascript
import { setTokens, clearTokens, getAccessToken } from '@/utils/request';

// 登录后设置 token
setTokens('access_token_here', 'refresh_token_here');

// 获取当前 access token
const token = getAccessToken();

// 清除所有 token（退出登录时）
clearTokens();
```

## 无感刷新机制

当 access token 过期时（收到 401 响应），系统会：

1. 自动使用 refresh token 获取新的 access token
2. 将失败的请求加入队列等待
3. 获取新 token 后重新发送队列中的请求
4. 如果 refresh token 也过期，则清除所有 token 并跳转到登录页

## 文件上传

```javascript
const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await api.upload('/upload', formData, {
    onUploadProgress: (progressEvent) => {
      const progress = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      console.log(`上传进度: ${progress}%`);
    },
  });

  return response.data;
};
```

## 文件下载

```javascript
const downloadFile = async (fileId, filename) => {
  const response = await api.download(`/files/${fileId}`);

  // 创建下载链接
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  link.remove();
  window.URL.revokeObjectURL(url);
};
```

## 错误处理

```javascript
try {
  const response = await api.get('/users');
  // 处理成功响应
} catch (error) {
  if (error.response) {
    // 服务器返回错误状态码
    console.error('Error status:', error.response.status);
    console.error('Error data:', error.response.data);
  } else if (error.request) {
    // 请求发送但没有收到响应
    console.error('No response received:', error.request);
  } else {
    // 其他错误
    console.error('Error:', error.message);
  }
}
```

## 配置说明

### 环境配置

- 开发环境：baseURL 设置为 `/api`
- 生产环境：baseURL 为空字符串，使用相对路径

### Token 存储

- Access Token: 存储在 `localStorage` 中，key 为 `access_token`
- Refresh Token: 存储在 `localStorage` 中，key 为 `refresh_token`

### 超时设置

- 默认请求超时时间：10 秒

## 注意事项

1. **刷新 Token API**: 需要后端提供 `/api/auth/refresh` 接口
2. **Token 格式**: 使用 Bearer Token 格式
3. **登录状态**: 使用 `userStatus` 标识用户登录状态
4. **路由跳转**: Token 过期时会自动跳转到 `/login` 页面

## 后端 API 要求

### 登录接口

```
POST /api/auth/login
Request: { username, password }
Response: {
  data: {
    accessToken: string,
    refreshToken: string
  }
}
```

### 刷新 Token 接口

```
POST /api/auth/refresh
Request: { refreshToken: string }
Response: {
  data: {
    accessToken: string,
    refreshToken?: string
  }
}
```

### 受保护的接口

```
Headers: { Authorization: "Bearer <access_token>" }
```

## 🚀 快速开始

### 1. 在登录页面使用

```javascript
// 参考 src/pages/login/form.new.tsx
import { login } from '@/services/auth';

const handleLogin = async (username, password) => {
  const result = await login(username, password);
  if (result.success) {
    // 登录成功，跳转到首页
    window.location.href = '/';
  } else {
    // 显示错误信息
    setErrorMessage(result.message);
  }
};
```

### 2. 在其他页面使用 API

```javascript
import api from '@/utils/request';

// 获取数据
const fetchData = async () => {
  try {
    const response = await api.get('/data/list');
    setData(response.data);
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};
```

### 3. 使用路由守卫

```javascript
// 在需要登录的页面使用
import AuthGuard from '@/components/AuthGuard';

function ProtectedPage() {
  return (
    <AuthGuard>
      <div>这是需要登录才能访问的页面</div>
    </AuthGuard>
  );
}
```

## 迁移指南

如果你的项目中已经在使用 axios，可以按以下步骤迁移：

1. 将现有的 `axios.get/post/put/delete` 替换为 `api.get/post/put/delete`
2. 在登录成功后调用 `setTokens()` 保存 token
3. 在退出登录时调用 `clearTokens()` 清除 token
4. 确保后端提供刷新 token 的接口
5. 使用 `AuthGuard` 组件保护需要登录的页面

这样就可以享受无感刷新 token 的便利了！

## 🔧 自定义配置

如果需要修改配置，可以编辑 `src/utils/request.js` 文件：

```javascript
// 修改超时时间
timeout: 15000, // 15秒

// 修改baseURL
baseURL: process.env.REACT_APP_API_BASE_URL || '/api',

// 修改token存储key
const ACCESS_TOKEN_KEY = 'my_access_token';
const REFRESH_TOKEN_KEY = 'my_refresh_token';
```
