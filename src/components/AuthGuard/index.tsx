import React, { useEffect, useState } from 'react';
import { Spin } from '@arco-design/web-react';
import { isAuthenticated, getUserInfo } from '@/services/auth';
import { getAccessToken } from '@/utils/request';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * 路由守卫组件
 * 用于保护需要登录才能访问的页面
 */
const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  fallback = <div>请先登录</div>,
}) => {
  const [loading, setLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 检查本地存储的登录状态和token
        const hasToken = !!getAccessToken();
        const localAuthStatus = isAuthenticated();

        if (!hasToken || !localAuthStatus) {
          setAuthenticated(false);
          setLoading(false);
          // 跳转到登录页
          window.location.href = '/login';
          return;
        }

        // 验证token是否有效（可选）
        try {
          await getUserInfo();
          setAuthenticated(true);
        } catch (error) {
          // token无效，清除本地状态
          localStorage.removeItem('userStatus');
          setAuthenticated(false);
          window.location.href = '/login';
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        setAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  if (loading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Spin size={40} />
      </div>
    );
  }

  if (!authenticated) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

export default AuthGuard;
